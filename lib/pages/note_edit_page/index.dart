import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../constants/app_colors.dart';
import '../../utils/note_webview_helper.dart';
import '../../widgets/custom_toast.dart';
import '../../components/publish_confirmation_dialog.dart';
import '../../api/api_provider.dart';
import 'models/note_edit_model.dart';
import 'widgets/note_webview.dart';
import 'widgets/note_loading.dart';
import 'widgets/note_error.dart';

import 'widgets/note_action_buttons.dart';
import 'widgets/note_bottom_panel.dart';

/// 编辑笔记页面
class NoteEditPage extends StatefulWidget {
  /// 笔记ID
  final String? noteId;

  const NoteEditPage({
    super.key,
    this.noteId,
  });

  @override
  State<NoteEditPage> createState() => _NoteEditPageState();
}

class _NoteEditPageState extends State<NoteEditPage> {
  /// 笔记数据模型
  NoteEditModel _noteModel = const NoteEditModel(noteId: '');

  /// 笔记WebView辅助类
  final NoteWebviewHelper _noteHelper = NoteWebviewHelper();

  /// 底部面板是否显示
  bool _isPanelVisible = false;

  /// 是否为预览模式
  bool _isPreviewMode = false;

  /// WebView组件的GlobalKey，用于调用其方法
  final GlobalKey<NoteWebviewState> _webviewKey = GlobalKey<NoteWebviewState>();

  bool _hasLoadedContent = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasLoadedContent) {
      _hasLoadedContent = true;
      _loadNoteContent();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }



  /// 加载笔记内容
  Future<void> _loadNoteContent() async {
    // 获取笔记ID
    final noteId = widget.noteId ?? '2'; // 默认使用测试ID

    // 获取路由参数
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final isDraft = args?['isDraft'] as bool? ?? false;
    final draftContent = args?['draftContent'] as String?;
    final draftTitle = args?['draftTitle'] as String?;
    final draftDesc = args?['draftDesc'] as String?;
    final draftCover = args?['draftCover'] as String?;

    debugPrint('编辑页面参数解析: noteId=$noteId, isDraft=$isDraft, draftContent长度=${draftContent?.length ?? 0}');
    debugPrint('草稿标题: $draftTitle, 草稿描述: $draftDesc');
    debugPrint('路由参数: $args');

    setState(() {
      _noteModel = _noteModel.copyWith(
        noteId: noteId,
        state: NoteEditState.loading,
        errorMessage: null,
      );
    });

    try {
      if (isDraft && draftContent != null && draftContent.isNotEmpty) {
        // 如果是草稿模式且有草稿内容，直接使用草稿内容
        debugPrint('使用草稿内容，长度: ${draftContent.length}');
        setState(() {
          _noteModel = _noteModel.copyWith(
            title: draftTitle ?? '无标题',
            htmlContent: draftContent,
            description: draftDesc ?? '',
            cover: draftCover ?? '',
            state: NoteEditState.success,
          );
        });
      } else {
        // 否则按原来的逻辑获取笔记内容
        // 首先尝试获取HTML内容
        final html = await _noteHelper.getNoteHtml(noteId);

        if (html != null && html.isNotEmpty) {
          // 如果有HTML内容，直接使用
          setState(() {
            _noteModel = _noteModel.copyWith(
              htmlContent: html,
              state: NoteEditState.success,
            );
          });
        } else {
          // 如果没有HTML内容，获取完整的笔记详情
          final noteDetail = await _noteHelper.getNoteDetail(noteId);

          if (noteDetail != null) {
            setState(() {
              _noteModel = NoteEditModel.fromJson(noteDetail);
            });
          } else {
            setState(() {
              _noteModel = _noteModel.copyWith(
                state: NoteEditState.error,
                errorMessage: '无法获取笔记内容',
              );
            });
          }
        }
      }
    } catch (e) {
      setState(() {
        _noteModel = _noteModel.copyWith(
          state: NoteEditState.error,
          errorMessage: '加载笔记内容失败: $e',
        );
      });
    }
  }

  /// WebView开始加载回调
  void _onWebViewPageStarted() {
    // 可以在这里添加额外的加载逻辑
  }

  /// WebView加载完成回调
  void _onWebViewPageFinished() {
    // 可以在这里添加加载完成后的逻辑
  }

  /// WebView加载错误回调
  void _onWebViewPageError(String error) {
    setState(() {
      _noteModel = _noteModel.copyWith(
        state: NoteEditState.error,
        errorMessage: error,
      );
    });
  }

  /// 重试加载
  void _onRetry() {
    _loadNoteContent();
  }

  /// 返回上一页
  void _onBack() {
    if (_isPreviewMode) {
      // 如果在预览模式，先退出预览模式
      setState(() {
        _isPreviewMode = false;
      });
      CustomToast.show('已退出预览模式');
    } else {
      Navigator.of(context).pop();
    }
  }

  /// 打开底部面板
  void _onOpenPanel() {
    setState(() {
      _isPanelVisible = true;
    });
  }

  /// 关闭底部面板
  void _onClosePanel() {
    setState(() {
      _isPanelVisible = false;
    });
  }

  /// 执行JavaScript脚本
  Future<void> _onExecuteScript(String script) async {
    final success = await _webviewKey.currentState?.executeScript(script);
    if (success == true) {
      print('JavaScript脚本执行成功');
    } else {
      print('JavaScript脚本执行失败');
    }
  }

  /// 获取最新DOM结构
  Future<String?> _onGetLatestDom() async {
    final domContent = await _webviewKey.currentState?.getLatestDom();
    if (domContent != null) {
      print('成功获取DOM结构，完整内容: $domContent');
      return domContent;
    } else {
      print('获取DOM结构失败');
      return null;
    }
  }

  /// 处理回滚操作
  void _onRollback() {
    // 暂时置空
  }

  /// 处理对比操作
  void _onCompare() {
    // 暂时置空
  }

  /// 处理预览操作
  void _onPreview() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
    });

    if (_isPreviewMode) {
      CustomToast.show('已进入预览模式，返回可退出预览');
    } else {
      CustomToast.show('已退出预览模式');
    }
  }

  /// 处理保存操作
  Future<void> _onSave() async {
    try {
      // 1. 显示二次确认弹窗
      final confirmed = await PublishConfirmationDialog.show(context);
      if (!confirmed) {
        return;
      }

      // 2. 获取当前页面的HTML结构
      final htmlContent = await _onGetLatestDom();
      if (htmlContent == null || htmlContent.isEmpty) {
        CustomToast.show('获取页面内容失败');
        return;
      }

      // 3. 调用更新笔记接口
      final apiProvider = ApiProvider();
      final noteId = _noteModel.noteId;

      if (noteId.isEmpty) {
        CustomToast.show('笔记ID无效');
        return;
      }

      CustomToast.show('正在发布笔记...');

      final response = await apiProvider.noteApi.updateNote(
        id: noteId,
        title: _noteModel.title,
        html: htmlContent,
      );

      // 4. 检查响应结果
      if (response['code'] == 0) {
        CustomToast.show('笔记发布成功');

        // 5. 跳转到笔记列表页面
        if (mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/notes',
            (route) => false,
          );
        }
      } else {
        final message = response['message'] ?? '发布失败';
        CustomToast.show(message);
      }
    } catch (e) {
      debugPrint('发布笔记失败: $e');
      CustomToast.show('发布笔记失败: $e');
    }
  }







  /// 构建应用栏（返回null表示不显示）
  PreferredSizeWidget? _buildAppBar() {
    return null;
  }

  /// 构建页面主体
  Widget _buildBody() {
    if (_noteModel.isLoading) {
      return const NoteLoading(message: '正在加载笔记内容...');
    }

    if (_noteModel.isError) {
      return NoteError(
        message: _noteModel.errorMessage ?? '未知错误',
        onRetry: _onRetry,
        onBack: _onBack,
      );
    }

    return NoteWebview(
      key: _webviewKey,
      noteModel: _noteModel,
      onPageStarted: _onWebViewPageStarted,
      onPageFinished: _onWebViewPageFinished,
      onPageError: _onWebViewPageError,
    );
  }

  /// 构建底部工具栏（返回null表示不显示）
  Widget? _buildBottomToolbar() {
    return null;
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return WillPopScope(
      onWillPop: () async {
        if (_isPanelVisible) {
          // 如果底部面板打开，先关闭面板
          setState(() {
            _isPanelVisible = false;
          });
          return false;
        } else if (_isPreviewMode) {
          // 如果在预览模式，退出预览模式
          setState(() {
            _isPreviewMode = false;
          });
          CustomToast.show('已退出预览模式');
          return false;
        }
        return true;
      },
      child: Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          _buildBody(),
          // 右侧操作按钮（预览模式时只显示预览按钮）
          NoteActionButtons(
            onEdit: _isPreviewMode ? null : _onOpenPanel,
            onRollback: _isPreviewMode ? null : _onRollback,
            onCompare: _isPreviewMode ? null : _onCompare,
            onPreview: _onPreview,
            onSave: _isPreviewMode ? null : _onSave,
            isPreviewMode: _isPreviewMode,
          ),
          // 底部面板组件
          NoteBottomPanel(
            isVisible: _isPanelVisible,
            onClose: _onClosePanel,
            htmlContent: _noteModel.htmlContent,
            onExecuteScript: _onExecuteScript,
            onGetLatestDom: _onGetLatestDom,
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomToolbar(),
    ),
    );
  }
}

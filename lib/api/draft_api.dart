import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../models/note_item.dart';

/// 草稿相关的API接口
class DraftApi {
  final ApiClient _apiClient;

  /// 构造函数
  /// [apiClient] API客户端实例
  DraftApi(this._apiClient);

  /// 获取草稿列表
  ///
  /// [page] 页码，从1开始，默认为1
  /// [pageSize] 每页数量，默认为10，最大100
  ///
  /// 返回草稿列表响应数据
  Future<DraftListResponse> getDraftList({
    int page = 1,
    int pageSize = 10,
  }) async {
    debugPrint('获取草稿列表: page=$page, pageSize=$pageSize');

    final response = await _apiClient.get('/note/draft/list?page=$page&page_size=$pageSize');

    // 检查响应状态
    if (response['code'] == 0) {
      return DraftListResponse.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 创建草稿
  ///
  /// [noteId] 笔记ID
  /// [content] 草稿内容
  ///
  /// 返回创建的草稿信息
  Future<DraftItem> createDraft({
    required String noteId,
    required String content,
  }) async {
    debugPrint('创建草稿: noteId=$noteId');

    final response = await _apiClient.post('/note/draft/create', {
      'note_id': noteId,
      'content': content,
    });

    // 检查响应状态
    if (response['code'] == 0) {
      return DraftItem.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 更新草稿
  ///
  /// [draftId] 草稿ID
  /// [content] 草稿内容
  ///
  /// 返回更新后的草稿信息
  Future<DraftItem> updateDraft({
    required String draftId,
    required String content,
  }) async {
    debugPrint('更新草稿: draftId=$draftId');

    final response = await _apiClient.put('/note/draft/update', {
      'id': draftId,
      'content': content,
    });

    // 检查响应状态
    if (response['code'] == 0) {
      return DraftItem.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 删除草稿
  ///
  /// [draftId] 草稿ID
  ///
  /// 返回删除结果
  Future<bool> deleteDraft(String draftId) async {
    debugPrint('删除草稿: draftId=$draftId');

    final response = await _apiClient.delete('/note/draft/delete?id=$draftId');

    // 检查响应状态
    if (response['code'] == 0) {
      return true;
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 获取草稿详情（通过草稿ID）
  ///
  /// [draftId] 草稿ID
  ///
  /// 返回草稿详情信息
  Future<DraftItem> getDraftDetail(String draftId) async {
    debugPrint('获取草稿详情: draftId=$draftId');

    final response = await _apiClient.get('/note/draft/detail?id=$draftId');

    // 检查响应状态
    if (response['code'] == 0) {
      return DraftItem.fromJson(response['data']);
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 获取草稿详情（通过笔记ID）
  ///
  /// [noteId] 笔记ID
  ///
  /// 返回草稿详情信息，如果没有草稿则返回null
  Future<DraftItem?> getDraftDetailByNoteId(String noteId) async {
    debugPrint('通过笔记ID获取草稿详情: noteId=$noteId');

    final response = await _apiClient.get('/note/draft/detail?note_id=$noteId');

    // 检查响应状态（与其他API保持一致，使用code=0表示成功）
    if (response['code'] == 0) {
      final data = response['data'];
      if (data != null && data['content'] != null) {
        return DraftItem.fromJson(data);
      } else {
        // 草稿内容为null，表示没有草稿
        debugPrint('草稿内容为null，该笔记没有草稿');
        return null;
      }
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }

  /// 发布草稿为笔记
  ///
  /// [draftId] 草稿ID
  ///
  /// 返回发布结果
  Future<bool> publishDraft(String draftId) async {
    debugPrint('发布草稿为笔记: draftId=$draftId');

    final response = await _apiClient.post('/note/draft/publish', {
      'id': draftId,
    });

    // 检查响应状态
    if (response['code'] == 0) {
      return true;
    } else {
      throw ApiException(
        code: response['code'],
        message: response['message'],
        data: response['data'],
      );
    }
  }
}
